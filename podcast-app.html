<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>NCast - Podcast App</title>
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Public Sans', -apple-system, BlinkMacSystemFont, sans-serif;
            background-color: #FFFFFF;
            color: #1F1F1F;
            line-height: 1.175;
        }

        .container {
            width: 428px;
            height: 1944px;
            margin: 0 auto;
            background: #FFFFFF;
            position: relative;
            overflow-x: hidden;
        }

        /* Header */
        .header {
            position: absolute;
            top: 48px;
            left: 32px;
            right: 32px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            height: 48px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .logo-icon {
            width: 50px;
            height: 45.69px;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo-text {
            font-family: 'Public Sans', sans-serif;
            font-weight: 700;
            font-size: 24px;
            line-height: 1.175;
            color: #4C0099;
        }

        .notification-container {
            position: relative;
            width: 48px;
            height: 48px;
        }

        .notification-bg {
            position: absolute;
            width: 48px;
            height: 48px;
            background: rgba(31, 31, 31, 0.1);
            border-radius: 24px;
        }

        .notification-btn {
            position: absolute;
            top: 13px;
            left: 13px;
            width: 21px;
            height: 21px;
            background: transparent;
            border: none;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .notification-badge {
            position: absolute;
            top: 2px;
            right: 2px;
            width: 12px;
            height: 12px;
            background: #FF5757;
            border-radius: 6px;
        }

        /* Search Bar */
        .search-container {
            position: absolute;
            top: 128px;
            left: 32px;
            right: 32px;
            height: 64px;
        }

        .search-bg {
            position: absolute;
            width: 100%;
            height: 64px;
            background: rgba(31, 31, 31, 0.08);
            border-radius: 32px;
        }

        .search-icon {
            position: absolute;
            left: 52px;
            top: 20px;
            width: 24px;
            height: 24px;
        }

        .search-input {
            position: absolute;
            left: 88px;
            top: 22px;
            right: 20px;
            height: 19px;
            background: transparent;
            border: none;
            outline: none;
            font-family: 'Public Sans', sans-serif;
            font-weight: 500;
            font-size: 16px;
            line-height: 1.175;
            color: #1F1F1F;
        }

        .search-input::placeholder {
            color: rgba(31, 31, 31, 0.5);
        }

        /* Carousel */
        .carousel-container {
            position: absolute;
            top: 272px;
            left: 32px;
            width: 331px;
            height: 200px;
        }

        .carousel-slide {
            position: absolute;
            width: 331px;
            height: 200px;
            border-radius: 24px;
            background-size: cover;
            background-position: center;
            display: none;
        }

        .carousel-slide.active {
            display: block;
        }

        .carousel-slide-1 {
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 331 200"><defs><linearGradient id="grad1" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23FF6B6B;stop-opacity:1" /><stop offset="100%" style="stop-color:%234ECDC4;stop-opacity:1" /></linearGradient></defs><rect width="331" height="200" fill="url(%23grad1)"/></svg>');
        }

        .carousel-slide-2 {
            background-image: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 364 200"><defs><linearGradient id="grad2" x1="0%" y1="0%" x2="100%" y2="100%"><stop offset="0%" style="stop-color:%23A8E6CF;stop-opacity:1" /><stop offset="100%" style="stop-color:%23FFD93D;stop-opacity:1" /></linearGradient></defs><rect width="364" height="200" fill="url(%23grad2)"/></svg>');
        }

        .carousel-dots {
            position: absolute;
            bottom: 16px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
        }

        .carousel-dot {
            width: 8px;
            height: 8px;
            border-radius: 4px;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: background 0.3s;
        }

        .carousel-dot.active {
            background: white;
        }

        /* Section Headers */
        .promoted-title {
            position: absolute;
            top: 224px;
            left: 32px;
            font-family: 'Public Sans', sans-serif;
            font-weight: 700;
            font-size: 20px;
            line-height: 1.175;
            color: #1F1F1F;
        }

        .trending-title {
            position: absolute;
            top: 504px;
            left: 32px;
            font-family: 'Public Sans', sans-serif;
            font-weight: 700;
            font-size: 20px;
            line-height: 1.175;
            color: #1F1F1F;
        }

        .trending-see-more {
            position: absolute;
            top: 507px;
            right: 32px;
            font-family: 'Public Sans', sans-serif;
            font-weight: 600;
            font-size: 16px;
            line-height: 1.175;
            color: rgba(31, 31, 31, 0.7);
            text-decoration: none;
        }

        .continue-title {
            position: absolute;
            top: 1040px;
            left: 32px;
            font-family: 'Public Sans', sans-serif;
            font-weight: 700;
            font-size: 24px;
            line-height: 1.175;
            color: #1F1F1F;
        }

        .continue-see-more {
            position: absolute;
            top: 1044px;
            right: 32px;
            font-family: 'Public Sans', sans-serif;
            font-weight: 600;
            font-size: 16px;
            line-height: 1.175;
            color: rgba(31, 31, 31, 0.7);
            text-decoration: none;
        }

        .categories-title {
            position: absolute;
            top: 1580px;
            left: 32px;
            font-family: 'Public Sans', sans-serif;
            font-weight: 700;
            font-size: 24px;
            line-height: 1.175;
            color: #1F1F1F;
        }

        .categories-see-more {
            position: absolute;
            top: 1585px;
            right: 32px;
            font-family: 'Public Sans', sans-serif;
            font-weight: 600;
            font-size: 16px;
            line-height: 1.175;
            color: rgba(31, 31, 31, 0.5);
            text-decoration: none;
        }

        /* Promoted Podcast Cards */
        .promoted-card-1 {
            position: absolute;
            top: 552px;
            left: 32px;
            width: 364px;
            height: 96px;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .promoted-card-2 {
            position: absolute;
            top: 792px;
            left: 32px;
            width: 364px;
            height: 96px;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .promoted-image {
            width: 108px;
            height: 96px;
            border-radius: 16px;
            background-size: cover;
            background-position: center;
        }

        .promoted-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .promoted-title-text {
            font-family: 'Public Sans', sans-serif;
            font-weight: 700;
            font-size: 16px;
            line-height: 1.175;
            color: #1F1F1F;
        }

        .promoted-category {
            font-family: 'Public Sans', sans-serif;
            font-weight: 400;
            font-size: 14px;
            line-height: 1.175;
            color: rgba(31, 31, 31, 0.7);
        }

        .promoted-duration {
            font-family: 'Public Sans', sans-serif;
            font-weight: 400;
            font-size: 14px;
            line-height: 1.175;
            color: rgba(31, 31, 31, 0.7);
        }

        .promoted-play-btn {
            width: 48px;
            height: 48px;
            background: rgba(76, 0, 153, 0.1);
            border-radius: 24px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            position: relative;
        }

        .play-triangle {
            width: 18px;
            height: 18px;
            background: #4C0099;
            clip-path: polygon(0 0, 100% 50%, 0 100%);
        }

        /* Trending Podcast Cards */
        .trending-card-1 {
            position: absolute;
            top: 672px;
            left: 32px;
            width: 364px;
            height: 96px;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .trending-card-2 {
            position: absolute;
            top: 912px;
            left: 32px;
            width: 108px;
            height: 96px;
        }

        .trending-card-2-info {
            position: absolute;
            top: 926px;
            left: 156px;
            width: 240px;
            height: 68px;
        }

        .trending-card-2-play {
            position: absolute;
            top: 951px;
            right: 32px;
            width: 48px;
            height: 48px;
        }

        /* Continue Listening */
        .continue-item-1 {
            position: absolute;
            top: 1092px;
            left: 32px;
            width: 364px;
            height: 96px;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .continue-item-2 {
            position: absolute;
            top: 1212px;
            left: 32px;
            width: 364px;
            height: 96px;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .continue-item-3 {
            position: absolute;
            top: 1332px;
            left: 32px;
            width: 364px;
            height: 96px;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .continue-item-4 {
            position: absolute;
            top: 1452px;
            left: 32px;
            width: 364px;
            height: 96px;
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .continue-image {
            width: 108px;
            height: 96px;
            border-radius: 16px;
            background-size: cover;
            background-position: center;
        }

        .continue-info {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .continue-title-text {
            font-family: 'Public Sans', sans-serif;
            font-weight: 700;
            font-size: 16px;
            line-height: 1.175;
            color: #1F1F1F;
        }

        .continue-remaining {
            font-family: 'Public Sans', sans-serif;
            font-weight: 400;
            font-size: 14px;
            line-height: 1.175;
            color: rgba(31, 31, 31, 0.7);
        }

        .progress-bar {
            width: 176px;
            height: 8px;
            background: #E9E9E9;
            border-radius: 4px;
            margin-top: 8px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: #FF5757;
            border-radius: 4px;
        }

        .continue-play-btn {
            width: 48px;
            height: 48px;
            background: rgba(76, 0, 153, 0.1);
            border-radius: 24px;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }

        /* Categories */
        .category-card-1 {
            position: absolute;
            top: 1632px;
            left: 32px;
            width: 200px;
            height: 120px;
            text-align: center;
        }

        .category-card-2 {
            position: absolute;
            top: 1632px;
            right: 32px;
            width: 200px;
            height: 120px;
            text-align: center;
        }

        .category-image {
            width: 200px;
            height: 120px;
            border-radius: 16px;
            background-size: cover;
            background-position: center;
        }

        .category-title-1 {
            position: absolute;
            top: 1768px;
            left: 32px;
            font-family: 'Public Sans', sans-serif;
            font-weight: 700;
            font-size: 16px;
            line-height: 1.175;
            color: #1F1F1F;
        }

        .category-title-2 {
            position: absolute;
            top: 1768px;
            right: 32px;
            font-family: 'Public Sans', sans-serif;
            font-weight: 700;
            font-size: 16px;
            line-height: 1.175;
            color: #1F1F1F;
        }

        .category-count-1 {
            position: absolute;
            top: 1795px;
            left: 32px;
            font-family: 'Public Sans', sans-serif;
            font-weight: 400;
            font-size: 14px;
            line-height: 1.175;
            color: rgba(31, 31, 31, 0.7);
        }

        .category-count-2 {
            position: absolute;
            top: 1795px;
            right: 32px;
            font-family: 'Public Sans', sans-serif;
            font-weight: 400;
            font-size: 14px;
            line-height: 1.175;
            color: rgba(31, 31, 31, 0.7);
        }

        /* Bottom Navigation */
        .bottom-nav-bg {
            position: absolute;
            top: 1809px;
            left: 0;
            width: 428px;
            height: 136px;
            background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.9) 100%);
        }

        .nav-container {
            position: absolute;
            top: 1841px;
            left: 32px;
            width: 364px;
            height: 72px;
            background: rgba(76, 0, 153, 0.1);
            backdrop-filter: blur(32px);
            border-radius: 48px;
            display: flex;
            align-items: center;
            justify-content: space-around;
        }

        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 4px;
            cursor: pointer;
            opacity: 0.5;
            transition: opacity 0.3s;
        }

        .nav-item.active {
            opacity: 1;
        }

        .nav-icon {
            width: 32px;
            height: 32px;
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .nav-item.active .nav-icon {
            background: #4C0099;
            color: white;
        }

        .nav-item:not(.active) .nav-icon {
            background: transparent;
            color: #1F1F1F;
        }

        .nav-dot {
            width: 5px;
            height: 5px;
            background: #4C0099;
            border-radius: 50%;
            opacity: 0;
        }

        .nav-item.active .nav-dot {
            opacity: 1;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- Header -->
        <header class="header">
            <div class="logo">
                <div class="logo-icon">
                    <!-- NCast Logo SVG -->
                    <svg width="50" height="46" viewBox="0 0 50 46" fill="none">
                        <rect width="50" height="46" rx="8" fill="#4C0099"/>
                        <g transform="translate(4.31, 11.36)">
                            <path d="M16.67 12.26V33.33H17.33V12.26H16.67Z" fill="white"/>
                            <path d="M17.33 0V21.07H16.67V0H17.33Z" fill="white"/>
                            <path d="M0 5.56V27.78H11.11V5.56H0Z" fill="white"/>
                            <path d="M38.89 5.56V27.78H50V5.56H38.89Z" fill="white"/>
                            <path d="M4.31 0H45.7V23.47H4.31V0Z" fill="white"/>
                        </g>
                    </svg>
                </div>
                <div class="logo-text">Ncast</div>
            </div>
            <div class="notification-container">
                <div class="notification-bg"></div>
                <button class="notification-btn">
                    <svg width="21" height="21" viewBox="0 0 21 21" fill="none">
                        <path d="M3 2L16 11V7.5C16 4.5 13.5 2 10.5 2C7.5 2 5 4.5 5 7.5V11L3 13V14H18V13L16 11V7.5Z" fill="#1F1F1F"/>
                        <path d="M4.53 15.81C4.53 17.13 5.85 18.13 7.15 18.13H13.77C15.07 18.13 16.39 17.13 16.39 15.81" fill="#1F1F1F"/>
                    </svg>
                </button>
                <div class="notification-badge"></div>
            </div>
        </header>

        <!-- Search Bar -->
        <div class="search-container">
            <div class="search-bg"></div>
            <svg class="search-icon" viewBox="0 0 24 24" fill="none">
                <circle cx="11" cy="11" r="8" stroke="#1F1F1F" stroke-width="2.5"/>
                <line x1="16.62" y1="15.93" x2="21" y2="20.31" stroke="#1F1F1F" stroke-width="2.5"/>
            </svg>
            <input type="text" class="search-input" placeholder="Search the podcast here...">
        </div>

        <!-- Carousel -->
        <div class="carousel-container">
            <div class="carousel" id="carousel">
                <div class="carousel-slide active" style="background: linear-gradient(135deg, #FF6B6B, #4ECDC4);"></div>
                <div class="carousel-slide" style="background: linear-gradient(135deg, #A8E6CF, #FFD93D);"></div>
                <div class="carousel-slide" style="background: linear-gradient(135deg, #FF8A80, #81C784);"></div>
                <div class="carousel-dots">
                    <div class="carousel-dot active" onclick="showSlide(0)"></div>
                    <div class="carousel-dot" onclick="showSlide(1)"></div>
                    <div class="carousel-dot" onclick="showSlide(2)"></div>
                </div>
            </div>
        </div>

        <!-- Promoted Podcasts -->
        <div class="section-header">
            <h2 class="section-title">Promoted Podcasts</h2>
        </div>
        <div class="podcast-grid">
            <div class="podcast-card">
                <div class="podcast-image" style="background: linear-gradient(135deg, #667eea, #764ba2);"></div>
                <div class="podcast-info">
                    <div class="podcast-title">See Mama Be</div>
                    <div class="podcast-category">Creative Studio</div>
                    <div class="podcast-duration">15 min</div>
                </div>
                <button class="play-btn"></button>
            </div>
            <div class="podcast-card">
                <div class="podcast-image" style="background: linear-gradient(135deg, #f093fb, #f5576c);"></div>
                <div class="podcast-info">
                    <div class="podcast-title">Your Time</div>
                    <div class="podcast-category">Educational</div>
                    <div class="podcast-duration">25 min</div>
                </div>
                <button class="play-btn"></button>
            </div>
        </div>

        <!-- Trending Podcasts -->
        <div class="section-header">
            <h2 class="section-title">Trending Podcasts</h2>
            <a href="#" class="see-more">See more</a>
        </div>
        <div class="podcast-grid">
            <div class="podcast-card">
                <div class="podcast-image" style="background: linear-gradient(135deg, #a8edea, #fed6e3);"></div>
                <div class="podcast-info">
                    <div class="podcast-title">Festival on the Beach</div>
                    <div class="podcast-category">Rock Electrics</div>
                    <div class="podcast-duration">10 min</div>
                </div>
                <button class="play-btn"></button>
            </div>
            <div class="podcast-card">
                <div class="podcast-image" style="background: linear-gradient(135deg, #ffecd2, #fcb69f);"></div>
                <div class="podcast-info">
                    <div class="podcast-title">Music Theme</div>
                    <div class="podcast-category">Lo-fi Music</div>
                    <div class="podcast-duration">20 min</div>
                </div>
                <button class="play-btn"></button>
            </div>
        </div>

        <!-- Continue Listening -->
        <div class="section-header">
            <h2 class="section-title">Continue Listening</h2>
            <a href="#" class="see-more">See more</a>
        </div>
        <div class="continue-section">
            <div class="continue-item">
                <div class="continue-image" style="background: linear-gradient(135deg, #ff9a9e, #fecfef);"></div>
                <div class="continue-info">
                    <div class="podcast-title">Talk Show - Ep7</div>
                    <div class="podcast-category">15 min remaining</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 65%;"></div>
                    </div>
                </div>
                <button class="play-btn"></button>
            </div>
            <div class="continue-item">
                <div class="continue-image" style="background: linear-gradient(135deg, #a18cd1, #fbc2eb);"></div>
                <div class="continue-info">
                    <div class="podcast-title">Musical Soul - Vol. 3</div>
                    <div class="podcast-category">35 min remaining</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 30%;"></div>
                    </div>
                </div>
                <button class="play-btn"></button>
            </div>
            <div class="continue-item">
                <div class="continue-image" style="background: linear-gradient(135deg, #fad0c4, #ffd1ff);"></div>
                <div class="continue-info">
                    <div class="podcast-title">Let's Stand Up</div>
                    <div class="podcast-category">5 min remaining</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 85%;"></div>
                    </div>
                </div>
                <button class="play-btn"></button>
            </div>
            <div class="continue-item">
                <div class="continue-image" style="background: linear-gradient(135deg, #ffecd2, #fcb69f);"></div>
                <div class="continue-info">
                    <div class="podcast-title">Talk Show - Ep10</div>
                    <div class="podcast-category">30 min remaining</div>
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: 45%;"></div>
                    </div>
                </div>
                <button class="play-btn"></button>
            </div>
        </div>

        <!-- Top Categories -->
        <div class="section-header">
            <h2 class="section-title">Top Categories</h2>
            <a href="#" class="see-more">See more</a>
        </div>
        <div class="categories-grid">
            <div class="category-card">
                <div class="category-image" style="background: linear-gradient(135deg, #667eea, #764ba2);"></div>
                <div class="category-title">Business</div>
                <div class="category-count">125 podcasts</div>
            </div>
            <div class="category-card">
                <div class="category-image" style="background: linear-gradient(135deg, #f093fb, #f5576c);"></div>
                <div class="category-title">Healthy Lifestyle</div>
                <div class="category-count">158 podcasts</div>
            </div>
        </div>

        <!-- Bottom Navigation -->
        <div class="bottom-nav">
            <div class="nav-container">
                <div class="nav-item">
                    <div class="nav-icon">🧭</div>
                    <div class="nav-dot"></div>
                </div>
                <div class="nav-item active">
                    <div class="nav-icon">🎧</div>
                    <div class="nav-dot"></div>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">❤️</div>
                    <div class="nav-dot"></div>
                </div>
                <div class="nav-item">
                    <div class="nav-icon">👤</div>
                    <div class="nav-dot"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const slides = document.querySelectorAll('.carousel-slide');
        const dots = document.querySelectorAll('.carousel-dot');

        function showSlide(index) {
            slides[currentSlide].classList.remove('active');
            dots[currentSlide].classList.remove('active');
            
            currentSlide = index;
            
            slides[currentSlide].classList.add('active');
            dots[currentSlide].classList.add('active');
        }

        // Auto-play carousel
        setInterval(() => {
            const nextSlide = (currentSlide + 1) % slides.length;
            showSlide(nextSlide);
        }, 3000);

        // Navigation
        document.querySelectorAll('.nav-item').forEach((item, index) => {
            item.addEventListener('click', () => {
                document.querySelector('.nav-item.active').classList.remove('active');
                item.classList.add('active');
            });
        });
    </script>
</body>
</html>
